<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Progress Bar -->
    <RegistrationProgress :current-step="2" />

    <!-- Main Content -->
    <section class="py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
          <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white">
            Maklumat Sekolah
          </h2>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">
            Langkah 2: Isi maklumat sekolah dan pentadbir
          </p>
        </div>

        <!-- Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
          <form @submit.prevent="handleSubmit" class="p-6 space-y-8">

            <!-- School Information Section -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Maklumat Sekolah
              </h3>
              <div class="space-y-4">
                <!-- School Name -->
                <div>
                  <label for="schoolName" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Nama Sekolah *
                  </label>
                  <input id="schoolName" v-model="formData.schoolName" type="text" required
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Contoh: Sekolah Kebangsaan Taman Desa" :disabled="loading" />
                </div>

                <!-- School Code -->
                <div>
                  <label for="schoolCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Kod Sekolah *
                  </label>
                  <input id="schoolCode" v-model="formData.schoolCode" type="text" required
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Contoh: sktamandesa" :disabled="loading" @blur="validateSchoolCode" />
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    URL sekolah anda: <span class="font-medium">{{ schoolUrl }}</span>
                  </p>
                  <!-- School Code Validation -->
                  <div v-if="schoolCodeError" class="mt-1 text-sm text-red-600 dark:text-red-400">
                    {{ schoolCodeError }}
                  </div>
                  <div v-if="schoolCodeValid" class="mt-1 text-sm text-green-600 dark:text-green-400">
                    ✓ Kod sekolah tersedia
                  </div>
                </div>

                <!-- School Address -->
                <div>
                  <label for="schoolAddress" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Alamat Sekolah
                  </label>
                  <textarea id="schoolAddress" v-model="formData.schoolAddress" rows="3"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Alamat lengkap sekolah (pilihan)" :disabled="loading"></textarea>
                </div>
              </div>
            </div>

            <!-- Administrator Information Section -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Maklumat Pentadbir Sekolah
              </h3>
              <div class="space-y-4">
                <!-- Admin Full Name -->
                <div>
                  <label for="adminName" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Nama Penuh *
                  </label>
                  <input id="adminName" v-model="formData.adminName" type="text" required
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Nama penuh pentadbir sekolah" :disabled="loading" />
                </div>

                <!-- Admin Email (read-only) -->
                <div>
                  <label for="adminEmail" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Alamat Emel
                  </label>
                  <input id="adminEmail" v-model="adminEmail" type="email" readonly
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-gray-600 text-gray-500 dark:text-gray-400" />
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Emel yang digunakan untuk pendaftaran
                  </p>
                </div>
              </div>
            </div>

            <!-- Error Message -->
            <div v-if="errorMessage"
              class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
              <p class="text-sm text-red-600 dark:text-red-400">{{ errorMessage }}</p>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-between pt-6">
              <button type="button" @click="goBack"
                class="flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <Icon name="heroicons:arrow-left-20-solid" class="w-4 h-4 mr-2" />
                Kembali
              </button>

              <button type="submit" :disabled="loading || !isFormValid"
                class="flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                <Icon v-if="loading" name="heroicons:arrow-path-20-solid" class="animate-spin -ml-1 mr-2 h-4 w-4" />
                {{ loading ? 'Menyimpan...' : 'Teruskan ke Pembayaran' }}
                <Icon v-if="!loading" name="heroicons:arrow-right-20-solid" class="w-4 h-4 ml-2" />
              </button>
            </div>
          </form>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  layout: 'landing'
})

// Reactive data
const formData = ref({
  schoolName: '',
  schoolCode: '',
  schoolAddress: '',
  adminName: ''
})

const loading = ref(false)
const errorMessage = ref('')
const schoolCodeError = ref('')
const schoolCodeValid = ref(false)
const adminEmail = ref('')
const sessionToken = ref('')

// Supabase client
const { client: supabase } = useSupabase()

// Computed properties
const schoolUrl = computed(() => {
  if (!formData.value.schoolCode) return '.rphmate.com'
  return `${formData.value.schoolCode.toLowerCase()}.rphmate.com`
})

const isFormValid = computed(() => {
  return formData.value.schoolName.trim() &&
    formData.value.schoolCode.trim() &&
    formData.value.adminName.trim() &&
    !schoolCodeError.value &&
    schoolCodeValid.value
})

// Load existing data on mount
onMounted(async () => {
  sessionToken.value = sessionStorage.getItem('registration_session_token') || ''

  if (!sessionToken.value) {
    await navigateTo('/daftar')
    return
  }

  // Load existing pre-billing data
  const { data, error } = await supabase
    .from('pre_billing')
    .select('*')
    .eq('session_token', sessionToken.value)
    .single()

  if (error || !data) {
    await navigateTo('/daftar')
    return
  }

  // Check if email is verified (for non-Google users)
  if (!data.is_google_signup && !data.email_verified) {
    await navigateTo('/daftar/verify-email')
    return
  }

  // Populate form with existing data
  adminEmail.value = data.admin_email
  if (data.school_name) formData.value.schoolName = data.school_name
  if (data.school_code) formData.value.schoolCode = data.school_code
  if (data.school_address) formData.value.schoolAddress = data.school_address
  if (data.admin_full_name) formData.value.adminName = data.admin_full_name

  // If Google user and no admin name, get from user profile
  if (data.is_google_signup && !formData.value.adminName) {
    const { data: { user } } = await supabase.auth.getUser()
    if (user?.user_metadata?.full_name) {
      formData.value.adminName = user.user_metadata.full_name
    }
  }
})

// Validate school code
const validateSchoolCode = async () => {
  const code = formData.value.schoolCode.trim().toLowerCase()

  if (!code) {
    schoolCodeError.value = ''
    schoolCodeValid.value = false
    return
  }

  // Check format
  if (!/^[a-zA-Z0-9]+$/.test(code)) {
    schoolCodeError.value = 'Kod sekolah hanya boleh mengandungi huruf dan nombor'
    schoolCodeValid.value = false
    return
  }

  try {
    // Check uniqueness in database
    const { data, error } = await supabase
      .from('schools')
      .select('id')
      .eq('code', code)
      .single()

    if (error && error.code !== 'PGRST116') {
      schoolCodeError.value = 'Ralat memeriksa kod sekolah'
      schoolCodeValid.value = false
      return
    }

    if (data) {
      schoolCodeError.value = 'Kod sekolah ini telah digunakan'
      schoolCodeValid.value = false
    } else {
      schoolCodeError.value = ''
      schoolCodeValid.value = true
    }

  } catch (error) {
    schoolCodeError.value = 'Ralat memeriksa kod sekolah'
    schoolCodeValid.value = false
  }
}

// Handle form submission
const handleSubmit = async () => {
  if (!isFormValid.value) return

  loading.value = true
  errorMessage.value = ''

  try {
    // Update pre-billing data
    const { error } = await supabase
      .from('pre_billing')
      .update({
        school_name: formData.value.schoolName.trim(),
        school_code: formData.value.schoolCode.trim().toLowerCase(),
        school_address: formData.value.schoolAddress.trim() || null,
        admin_full_name: formData.value.adminName.trim(),
        phase_completed: 2,
        updated_at: new Date().toISOString()
      })
      .eq('session_token', sessionToken.value)

    if (error) {
      throw new Error('Ralat menyimpan maklumat')
    }

    // Proceed to payment phase
    await navigateTo('/pembayaran')

  } catch (error: any) {
    console.error('Save information error:', error)
    errorMessage.value = error.message || 'Ralat menyimpan maklumat. Sila cuba lagi.'
  } finally {
    loading.value = false
  }
}

// Go back to previous phase
const goBack = async () => {
  await navigateTo('/daftar')
}

// Set page head
useHead({
  title: 'Maklumat Sekolah - RPHMate',
  meta: [
    {
      name: 'description',
      content: 'Isi maklumat sekolah dan pentadbir untuk meneruskan pendaftaran.'
    }
  ]
})
</script>
