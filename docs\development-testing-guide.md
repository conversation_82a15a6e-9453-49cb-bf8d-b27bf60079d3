# RPHMate SaaS Development Testing Guide

## 🧪 Overview

This guide provides comprehensive testing procedures for the RPHMate multi-tenant SaaS platform during development. Follow these steps to ensure all features work correctly before production deployment.

## 🚀 Quick Start

### 1. **Environment Setup**
```bash
# Run the setup script
npm run setup:dev

# Seed test data
npm run seed:test

# Start development server
npm run dev
```

### 2. **Start Stripe Webhook Listener** (in separate terminal)
```bash
npm run stripe:listen
```

## 🌐 Testing URLs

### Main Domain (localhost:3000)
- **Landing Page**: http://localhost:3000
- **Pricing Page**: http://localhost:3000/pricing
- **Registration**: http://localhost:3000/billing
- **Admin Login**: http://localhost:3000/login

### Test Schools (subdomains)
- **XBA1224**: http://xba1224.localhost:3000
- **Demo School**: http://demo.localhost:3000
- **Test Academy**: http://test.localhost:3000

## 📋 Complete Testing Checklist

### Phase 1: Landing Pages Testing ✅
- [ ] **Landing Page** (http://localhost:3000)
  - [ ] Hero section loads correctly
  - [ ] Feature cards display properly
  - [ ] CTA buttons link to pricing page
  - [ ] Navigation works (pricing, login)
  - [ ] Dark mode toggle functions
  - [ ] Mobile responsive design

- [ ] **Pricing Page** (http://localhost:3000/pricing)
  - [ ] All 3 pricing tiers display correctly
  - [ ] Monthly/yearly toggle works
  - [ ] Prices update correctly with toggle
  - [ ] "Start Free Trial" buttons link to billing
  - [ ] FAQ section expands/collapses

- [ ] **School Admin Login** (http://localhost:3000/login)
  - [ ] Login form accepts input
  - [ ] Password visibility toggle works
  - [ ] "Forgot password" link present
  - [ ] School code direct access works
  - [ ] Error messages display correctly

### Phase 2: Subdomain Infrastructure Testing ✅
- [ ] **Subdomain Resolution**
  - [ ] xba1224.localhost:3000 resolves correctly
  - [ ] demo.localhost:3000 resolves correctly
  - [ ] test.localhost:3000 resolves correctly
  - [ ] Invalid subdomains show appropriate errors

- [ ] **Authentication Flows**
  - [ ] Unauthenticated users redirect to /auth/login
  - [ ] Login form works on school subdomains
  - [ ] Successful login redirects to /dashboard
  - [ ] Logout functionality works
  - [ ] Session persistence across page reloads

- [ ] **School-Specific Features**
  - [ ] School admin page (/admin) accessible
  - [ ] School context detected correctly
  - [ ] Data isolation between schools verified

### Phase 3: Payment Integration Testing ✅
- [ ] **Registration Flow**
  - [ ] Billing form accepts all required fields
  - [ ] School code validation works
  - [ ] Real-time URL preview updates
  - [ ] Form validation prevents submission with errors
  - [ ] Error messages display clearly

- [ ] **Stripe Integration**
  - [ ] Checkout session creation works
  - [ ] Redirect to Stripe checkout successful
  - [ ] Test card payments process correctly
  - [ ] Failed payments handled gracefully
  - [ ] Webhook events received and processed

- [ ] **Post-Payment Flow**
  - [ ] Success page displays after payment
  - [ ] School information shown correctly
  - [ ] Admin dashboard link works
  - [ ] School portal link functional

## 💳 Stripe Test Cards

Use these test cards for payment testing:

### Successful Payments
- **Visa**: 4242 4242 4242 4242
- **Visa (debit)**: 4000 0566 5566 5556
- **Mastercard**: 5555 5555 5555 4444

### Failed Payments
- **Generic decline**: 4000 0000 0000 0002
- **Insufficient funds**: 4000 0000 0000 9995
- **Lost card**: 4000 0000 0000 9987

### Authentication Required
- **3D Secure**: 4000 0025 0000 3155

**Note**: Use any future expiry date, any 3-digit CVC, and any billing postal code.

## 🔑 Test Credentials

### School Admin Accounts
```
No demo accounts available - schools must be created through payment flow.
Use the billing flow to create real school accounts for testing.

Test Academy (test.localhost:3000):
Email: <EMAIL>
Password: password123
```

### Teacher Accounts
```
XBA1224 School:
Email: <EMAIL>
Password: password123
```

## 🧪 Testing Scenarios

### Scenario 1: New School Registration
1. Visit http://localhost:3000
2. Click "Start Free Trial" → Pricing page
3. Select "Professional" plan → Billing page
4. Fill registration form with unique school code
5. Complete Stripe checkout with test card
6. Verify success page displays
7. Access admin dashboard
8. Visit school portal URL

### Scenario 2: Existing School Access
1. Visit http://xba1224.localhost:3000
2. Should redirect to /auth/login
3. Login with test credentials
4. Should redirect to /dashboard
5. Navigate to /admin page
6. Verify school-specific content

### Scenario 3: Cross-School Data Isolation
1. Login to xba1224.localhost:3000
2. Note available data/features
3. Logout and login to demo.localhost:3000
4. Verify completely different data set
5. Confirm no cross-contamination

### Scenario 4: Payment Failure Handling
1. Start registration process
2. Use declined test card (4000 0000 0000 0002)
3. Verify error handling
4. Retry with successful card
5. Confirm process completes

### Scenario 5: Webhook Processing
1. Monitor webhook listener terminal
2. Complete a test payment
3. Verify webhook events received:
   - checkout.session.completed
   - customer.subscription.created
   - invoice.payment_succeeded
4. Check school creation in logs

## 🐛 Common Issues & Solutions

### Subdomain Not Resolving
**Problem**: xba1224.localhost:3000 doesn't load
**Solution**: 
1. Check hosts file entries
2. Restart browser
3. Clear DNS cache: `ipconfig /flushdns` (Windows)

### Stripe Webhooks Not Working
**Problem**: Payments succeed but school not created
**Solution**:
1. Ensure Stripe CLI is running
2. Check webhook secret in .env.local
3. Verify webhook endpoint URL

### Authentication Issues
**Problem**: Login doesn't work or redirects incorrectly
**Solution**:
1. Check Supabase configuration
2. Verify environment variables
3. Clear browser cookies/localStorage

### TypeScript Errors
**Problem**: Development server shows TypeScript errors
**Solution**:
```bash
npm run typecheck
```
Fix any reported issues.

## 📊 Performance Testing

### Load Testing
- [ ] Test with multiple concurrent users
- [ ] Verify subdomain routing performance
- [ ] Check database query efficiency
- [ ] Monitor memory usage

### Browser Testing
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile browsers (iOS Safari, Chrome Mobile)

## 🔒 Security Testing

### Authentication Security
- [ ] Verify session isolation between schools
- [ ] Test unauthorized access attempts
- [ ] Check password security requirements
- [ ] Validate JWT token handling

### Payment Security
- [ ] Verify Stripe webhook signature validation
- [ ] Test with invalid payment data
- [ ] Check for data leakage in error messages
- [ ] Validate HTTPS enforcement

## 📝 Testing Reports

### Daily Testing Checklist
- [ ] All main URLs accessible
- [ ] Subdomain routing functional
- [ ] Payment flow working
- [ ] No console errors
- [ ] TypeScript check passes

### Pre-Deployment Testing
- [ ] Complete end-to-end flow testing
- [ ] Cross-browser compatibility verified
- [ ] Performance benchmarks met
- [ ] Security tests passed
- [ ] Documentation updated

## 🚀 Next Steps

After completing all testing:

1. **Document any issues** found during testing
2. **Fix critical bugs** before proceeding
3. **Update documentation** with any changes
4. **Prepare for production deployment**
5. **Set up monitoring and logging**

---

*This testing guide should be updated as new features are added or issues are discovered.*
