<template>
  <div
    class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
    <div class="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
      <div class="text-center">
        <!-- Loading State -->
        <div v-if="isProcessing" class="space-y-4">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Mengesahkan Emel...
          </h2>
          <p class="text-gray-600 dark:text-gray-300">
            Sila tunggu sebentar semasa kami mengesahkan akaun anda.
          </p>
        </div>

        <!-- Success State -->
        <div v-else-if="confirmationSuccess" class="space-y-4">
          <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor"
              viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Emel Berjaya Disahkan!
          </h2>
          <p class="text-gray-600 dark:text-gray-300">
            Akaun anda telah disahkan. Anda akan dialihkan ke langkah seterusnya.
          </p>
        </div>

        <!-- Error State -->
        <div v-else-if="errorMessage" class="space-y-4">
          <div class="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto">
            <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
          <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
            Ralat Pengesahan
          </h2>
          <p class="text-gray-600 dark:text-gray-300">
            {{ errorMessage }}
          </p>
          <button @click="goToRegistration"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
            Kembali ke Pendaftaran
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// Page metadata
definePageMeta({
  layout: false,
  title: 'Pengesahan Emel - RPHMate'
})

// Reactive state
const isProcessing = ref(true)
const confirmationSuccess = ref(false)
const errorMessage = ref('')

// Supabase client
const supabase = useSupabaseClient()

// Handle email confirmation
onMounted(async () => {
  console.log('=== EMAIL CONFIRMATION PAGE MOUNTED ===')

  try {
    // Get URL parameters
    const route = useRoute()
    console.log('Route query params:', route.query)

    // Check if we have the necessary parameters for email confirmation
    if (!route.query.access_token && !route.query.refresh_token) {
      throw new Error('Parameter pengesahan tidak dijumpai. Sila cuba lagi.')
    }

    // Wait a moment for Supabase to process the confirmation
    console.log('Waiting for Supabase to process email confirmation...')
    await new Promise(resolve => setTimeout(resolve, 3000))

    // Try to get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    console.log('User after confirmation:', user)
    console.log('User error:', userError)

    if (userError) {
      throw new Error(`Ralat pengesahan: ${userError.message}`)
    }

    if (!user) {
      throw new Error('Pengesahan gagal. Sila cuba lagi.')
    }

    if (!user.email_confirmed_at) {
      throw new Error('Emel belum disahkan. Sila semak emel anda.')
    }

    console.log('Email confirmed successfully for:', user.email)

    // Find the pre-billing record for this user
    const { data: preBillingData, error: preBillingError } = await supabase
      .from('pre_billing')
      .select('session_token, email_verified, phase_completed')
      .eq('admin_email', user.email!)
      .order('created_at', { ascending: false })
      .limit(1)
      .single()

    console.log('Pre-billing search result:', { preBillingData, preBillingError })

    if (preBillingError || !preBillingData) {
      throw new Error('Rekod pendaftaran tidak dijumpai. Sila daftar semula.')
    }

    // Update email verification status
    const { error: updateError } = await (supabase as any)
      .from('pre_billing')
      .update({ email_verified: true })
      .eq('session_token', (preBillingData as any).session_token)

    if (updateError) {
      console.error('Error updating email verification:', updateError)
      // Don't throw error here, just log it
    }

    // Store session token for the next step
    sessionStorage.setItem('registration_session_token', (preBillingData as any).session_token)
    console.log('Session token stored:', (preBillingData as any).session_token)

    // Show success state
    confirmationSuccess.value = true
    isProcessing.value = false

    // Redirect to maklumat page after a short delay
    setTimeout(() => {
      navigateTo('/daftar/maklumat')
    }, 2000)

  } catch (error: any) {
    console.error('Email confirmation error:', error)
    errorMessage.value = error.message || 'Ralat tidak diketahui berlaku.'
    isProcessing.value = false
  }
})

// Go back to registration
const goToRegistration = () => {
  navigateTo('/daftar')
}
</script>
