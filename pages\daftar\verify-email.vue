<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Progress Bar -->
    <RegistrationProgress :current-step="1" />

    <!-- Main Content -->
    <section class="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        <!-- Header -->
        <div class="text-center">
          <div
            class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 dark:bg-blue-900/30 mb-4">
            <Icon name="heroicons:envelope-20-solid" class="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white">
            Sahkan Emel Anda
          </h2>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">
            Ka<PERSON> telah menghantar pautan pengesahan ke emel anda
          </p>
        </div>

        <!-- Email Display -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div class="text-center space-y-4">
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <p class="text-sm text-gray-600 dark:text-gray-300 mb-2">Emel pengesahan dihantar ke:</p>
              <p class="font-medium text-gray-900 dark:text-white">{{ userEmail }}</p>
            </div>

            <div class="text-sm text-gray-600 dark:text-gray-300 space-y-2">
              <p>Sila semak peti masuk anda dan klik pautan pengesahan untuk meneruskan.</p>
              <p>Jika anda tidak menerima emel dalam beberapa minit, sila semak folder spam anda.</p>
            </div>

            <!-- Resend Email Button -->
            <div class="pt-4">
              <button @click="resendVerificationEmail" :disabled="loading || cooldownActive"
                class="w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                <Icon v-if="loading" name="heroicons:arrow-path-20-solid" class="animate-spin -ml-1 mr-2 h-4 w-4" />
                {{ getResendButtonText() }}
              </button>
            </div>

            <!-- Success Message -->
            <div v-if="successMessage"
              class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-3">
              <p class="text-sm text-green-600 dark:text-green-400">{{ successMessage }}</p>
            </div>

            <!-- Error Message -->
            <div v-if="errorMessage"
              class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
              <p class="text-sm text-red-600 dark:text-red-400">{{ errorMessage }}</p>
            </div>
          </div>
        </div>

        <!-- Instructions -->
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <h3 class="text-sm font-medium text-blue-900 dark:text-blue-200 mb-2">
            Langkah seterusnya:
          </h3>
          <ol class="text-sm text-blue-700 dark:text-blue-300 space-y-1 list-decimal list-inside">
            <li>Buka emel pengesahan dalam peti masuk anda</li>
            <li>Klik pautan "Sahkan Emel" dalam emel tersebut</li>
            <li>Anda akan diarahkan kembali untuk meneruskan pendaftaran</li>
          </ol>
        </div>

        <!-- Footer Links -->
        <div class="text-center space-y-2">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Ingin menggunakan emel lain?
            <button @click="goBackToRegistration"
              class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400">
              Kembali ke pendaftaran
            </button>
          </p>
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Sudah mempunyai akaun?
            <NuxtLink to="/login" class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400">
              Log masuk di sini
            </NuxtLink>
          </p>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  layout: 'landing'
})

// Reactive data
const loading = ref(false)
const errorMessage = ref('')
const successMessage = ref('')
const cooldownActive = ref(false)
const cooldownSeconds = ref(0)
const userEmail = ref('')

// Supabase client
const { client: supabase } = useSupabase()

// Get user email on mount
onMounted(async () => {
  // Try to get email from session storage or current user
  const sessionToken = sessionStorage.getItem('registration_session_token')

  if (sessionToken) {
    // Get email from pre_billing table
    const { data, error } = await supabase
      .from('pre_billing')
      .select('admin_email')
      .eq('session_token', sessionToken)
      .single()

    if (data) {
      userEmail.value = data.admin_email
    }
  }

  // Fallback: get from current user
  if (!userEmail.value) {
    const { data: { user } } = await supabase.auth.getUser()
    if (user?.email) {
      userEmail.value = user.email
    }
  }

  // If still no email, redirect back to registration
  if (!userEmail.value) {
    await navigateTo('/daftar')
  }
})

// Resend verification email
const resendVerificationEmail = async () => {
  if (cooldownActive.value || !userEmail.value) return

  loading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    const { error } = await supabase.auth.resend({
      type: 'signup',
      email: userEmail.value,
      options: {
        emailRedirectTo: `${window.location.origin}/daftar/verify-email`
      }
    })

    if (error) {
      throw new Error(error.message)
    }

    successMessage.value = 'Emel pengesahan telah dihantar semula. Sila semak peti masuk anda.'

    // Start cooldown
    startCooldown()

  } catch (error: any) {
    console.error('Resend email error:', error)
    errorMessage.value = error.message || 'Ralat menghantar semula emel. Sila cuba lagi.'
  } finally {
    loading.value = false
  }
}

// Start cooldown timer
const startCooldown = () => {
  cooldownActive.value = true
  cooldownSeconds.value = 60

  const timer = setInterval(() => {
    cooldownSeconds.value--

    if (cooldownSeconds.value <= 0) {
      clearInterval(timer)
      cooldownActive.value = false
    }
  }, 1000)
}

// Get resend button text
const getResendButtonText = () => {
  if (loading.value) return 'Menghantar...'
  if (cooldownActive.value) return `Hantar Semula (${cooldownSeconds.value}s)`
  return 'Hantar Semula Emel Pengesahan'
}

// Go back to registration
const goBackToRegistration = async () => {
  // Clear session data
  sessionStorage.removeItem('registration_session_token')

  // Sign out current user
  await supabase.auth.signOut()

  // Redirect to registration
  await navigateTo('/daftar')
}

// Check for email verification on page load
onMounted(async () => {
  // Check if user is already verified
  const { data: { user } } = await supabase.auth.getUser()

  if (user?.email_confirmed_at) {
    // Email is verified, update pre_billing and proceed
    const sessionToken = sessionStorage.getItem('registration_session_token')

    if (sessionToken) {
      await supabase
        .from('pre_billing')
        .update({ email_verified: true })
        .eq('session_token', sessionToken)
    }

    // Redirect to next phase
    await navigateTo('/daftar/maklumat')
  }
})

// Listen for auth state changes
supabase.auth.onAuthStateChange(async (event, session) => {
  if (event === 'SIGNED_IN' && session?.user?.email_confirmed_at) {
    // Email verified, proceed to next phase
    const sessionToken = sessionStorage.getItem('registration_session_token')

    if (sessionToken) {
      await supabase
        .from('pre_billing')
        .update({ email_verified: true })
        .eq('session_token', sessionToken)
    }

    await navigateTo('/daftar/maklumat')
  }
})

// Set page head
useHead({
  title: 'Sahkan Emel - RPHMate',
  meta: [
    {
      name: 'description',
      content: 'Sahkan alamat emel anda untuk meneruskan pendaftaran akaun sekolah.'
    }
  ]
})
</script>
