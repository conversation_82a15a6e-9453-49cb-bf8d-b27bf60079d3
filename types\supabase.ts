export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      pre_billing: {
        Row: {
          admin_email: string
          admin_full_name: string | null
          billing_complete: boolean | null
          created_at: string
          email_verified: boolean | null
          expires_at: string
          id: string
          is_google_signup: boolean | null
          phase_completed: number | null
          school_address: string | null
          school_code: string | null
          school_name: string | null
          selected_plan: string | null
          session_token: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          admin_email: string
          admin_full_name?: string | null
          billing_complete?: boolean | null
          created_at?: string
          email_verified?: boolean | null
          expires_at?: string
          id?: string
          is_google_signup?: boolean | null
          phase_completed?: number | null
          school_address?: string | null
          school_code?: string | null
          school_name?: string | null
          selected_plan?: string | null
          session_token: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          admin_email?: string
          admin_full_name?: string | null
          billing_complete?: boolean | null
          created_at?: string
          email_verified?: boolean | null
          expires_at?: string
          id?: string
          is_google_signup?: boolean | null
          phase_completed?: number | null
          school_address?: string | null
          school_code?: string | null
          school_name?: string | null
          selected_plan?: string | null
          session_token?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      profiles: {
        Row: {
          id: string
          full_name: string | null
          email: string | null
          is_school_admin: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          full_name?: string | null
          email?: string | null
          is_school_admin?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          full_name?: string | null
          email?: string | null
          is_school_admin?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      schools: {
        Row: {
          admin_user_id: string | null
          code: string
          created_at: string | null
          description: string | null
          established: number | null
          id: string
          is_active: boolean | null
          language: string | null
          last_tested: string | null
          location: string | null
          name: string
          next_billing_date: string | null
          response_time: string | null
          settings: Json | null
          subdomain_status: string | null
          subscription_plan: string | null
          subscription_status: string | null
          timezone: string | null
          total_revenue: number | null
          updated_at: string | null
        }
        Insert: {
          admin_user_id?: string | null
          code: string
          created_at?: string | null
          description?: string | null
          established?: number | null
          id?: string
          is_active?: boolean | null
          language?: string | null
          last_tested?: string | null
          location?: string | null
          name: string
          next_billing_date?: string | null
          response_time?: string | null
          settings?: Json | null
          subdomain_status?: string | null
          subscription_plan?: string | null
          subscription_status?: string | null
          timezone?: string | null
          total_revenue?: number | null
          updated_at?: string | null
        }
        Update: {
          admin_user_id?: string | null
          code?: string
          created_at?: string | null
          description?: string | null
          established?: number | null
          id?: string
          is_active?: boolean | null
          language?: string | null
          last_tested?: string | null
          location?: string | null
          name?: string
          next_billing_date?: string | null
          response_time?: string | null
          settings?: Json | null
          subdomain_status?: string | null
          subscription_plan?: string | null
          subscription_status?: string | null
          timezone?: string | null
          total_revenue?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

// Simplified type exports for better compatibility
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']