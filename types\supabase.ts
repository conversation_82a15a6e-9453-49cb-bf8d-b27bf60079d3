export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      academic_calendar_documents: {
        Row: {
          created_at: string
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id: string
          storage_file_path: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id?: string
          storage_file_path: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          file_mime_type?: string
          file_name?: string
          file_size_bytes?: number
          id?: string
          storage_file_path?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "academic_calendar_documents_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      items: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          title: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          title?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          title?: string | null
        }
        Relationships: []
      }
      failed_payments: {
        Row: {
          admin_data: Json
          created_at: string
          currency: string | null
          error_details: Json | null
          id: string
          last_retry_at: string | null
          max_retries: number | null
          next_retry_at: string | null
          payment_amount: number
          resolution_notes: string | null
          resolved_at: string | null
          resolved_by: string | null
          retry_count: number | null
          school_data: Json
          status: string | null
          stripe_customer_id: string | null
          stripe_session_id: string
          updated_at: string
        }
        Insert: {
          admin_data: Json
          created_at?: string
          currency?: string | null
          error_details?: Json | null
          id?: string
          last_retry_at?: string | null
          max_retries?: number | null
          next_retry_at?: string | null
          payment_amount: number
          resolution_notes?: string | null
          resolved_at?: string | null
          resolved_by?: string | null
          retry_count?: number | null
          school_data: Json
          status?: string | null
          stripe_customer_id?: string | null
          stripe_session_id: string
          updated_at?: string
        }
        Update: {
          admin_data?: Json
          created_at?: string
          currency?: string | null
          error_details?: Json | null
          id?: string
          last_retry_at?: string | null
          max_retries?: number | null
          next_retry_at?: string | null
          payment_amount?: number
          resolution_notes?: string | null
          resolved_at?: string | null
          resolved_by?: string | null
          retry_count?: number | null
          school_data?: Json
          status?: string | null
          stripe_customer_id?: string | null
          stripe_session_id?: string
          updated_at?: string
        }
        Relationships: []
      }
      lesson_plan_detailed_reflections: {
        Row: {
          action_items: Json | null
          activity_effectiveness: number | null
          additional_notes: string | null
          challenges_faced: string | null
          class_subject_id: string
          created_at: string
          day: string
          id: string
          improvements_needed: string | null
          jumlah_murid_mencapai_objektif: number | null
          lesson_plan_id: string
          objectives_achieved: boolean
          overall_rating: number
          resource_adequacy: string | null
          student_engagement: number | null
          successful_strategies: string | null
          tidak_terlaksana: string | null
          time_management: string | null
          tindakan_susulan: Json | null
          updated_at: string
        }
        Insert: {
          action_items?: Json | null
          activity_effectiveness?: number | null
          additional_notes?: string | null
          challenges_faced?: string | null
          class_subject_id: string
          created_at?: string
          day: string
          id?: string
          improvements_needed?: string | null
          jumlah_murid_mencapai_objektif?: number | null
          lesson_plan_id: string
          objectives_achieved?: boolean
          overall_rating: number
          resource_adequacy?: string | null
          student_engagement?: number | null
          successful_strategies?: string | null
          tidak_terlaksana?: string | null
          time_management?: string | null
          tindakan_susulan?: Json | null
          updated_at?: string
        }
        Update: {
          action_items?: Json | null
          activity_effectiveness?: number | null
          additional_notes?: string | null
          challenges_faced?: string | null
          class_subject_id?: string
          created_at?: string
          day?: string
          id?: string
          improvements_needed?: string | null
          jumlah_murid_mencapai_objektif?: number | null
          lesson_plan_id?: string
          objectives_achieved?: boolean
          overall_rating?: number
          resource_adequacy?: string | null
          student_engagement?: number | null
          successful_strategies?: string | null
          tidak_terlaksana?: string | null
          time_management?: string | null
          tindakan_susulan?: Json | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "lesson_plan_detailed_reflections_lesson_plan_id_fkey"
            columns: ["lesson_plan_id"]
            isOneToOne: false
            referencedRelation: "lesson_plans"
            referencedColumns: ["id"]
          },
        ]
      }
      lesson_plan_reflections: {
        Row: {
          action_items: string | null
          activity_effectiveness: number | null
          challenges_faced: string | null
          created_at: string | null
          id: string
          improvements_needed: string | null
          is_detailed_mode: boolean
          lesson_plan_id: string
          notes: string | null
          objectives_achieved: boolean
          overall_rating: number
          reflection_date: string
          resource_adequacy: number | null
          student_engagement: number | null
          student_feedback: string | null
          successful_strategies: string | null
          time_management: number | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          action_items?: string | null
          activity_effectiveness?: number | null
          challenges_faced?: string | null
          created_at?: string | null
          id?: string
          improvements_needed?: string | null
          is_detailed_mode?: boolean
          lesson_plan_id: string
          notes?: string | null
          objectives_achieved?: boolean
          overall_rating: number
          reflection_date?: string
          resource_adequacy?: number | null
          student_engagement?: number | null
          student_feedback?: string | null
          successful_strategies?: string | null
          time_management?: number | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          action_items?: string | null
          activity_effectiveness?: number | null
          challenges_faced?: string | null
          created_at?: string | null
          id?: string
          improvements_needed?: string | null
          is_detailed_mode?: boolean
          lesson_plan_id?: string
          notes?: string | null
          objectives_achieved?: boolean
          overall_rating?: number
          reflection_date?: string
          resource_adequacy?: number | null
          student_engagement?: number | null
          student_feedback?: string | null
          successful_strategies?: string | null
          time_management?: number | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "lesson_plan_reflections_lesson_plan_id_fkey"
            columns: ["lesson_plan_id"]
            isOneToOne: false
            referencedRelation: "lesson_plans"
            referencedColumns: ["id"]
          },
        ]
      }
      pre_billing: {
        Row: {
          admin_email: string
          admin_full_name: string | null
          billing_complete: boolean | null
          created_at: string
          email_verified: boolean | null
          expires_at: string
          id: string
          is_google_signup: boolean | null
          phase_completed: number | null
          school_address: string | null
          school_code: string | null
          school_name: string | null
          selected_plan: string | null
          session_token: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          admin_email: string
          admin_full_name?: string | null
          billing_complete?: boolean | null
          created_at?: string
          email_verified?: boolean | null
          expires_at?: string
          id?: string
          is_google_signup?: boolean | null
          phase_completed?: number | null
          school_address?: string | null
          school_code?: string | null
          school_name?: string | null
          selected_plan?: string | null
          session_token: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          admin_email?: string
          admin_full_name?: string | null
          billing_complete?: boolean | null
          created_at?: string
          email_verified?: boolean | null
          expires_at?: string
          id?: string
          is_google_signup?: boolean | null
          phase_completed?: number | null
          school_address?: string | null
          school_code?: string | null
          school_name?: string | null
          selected_plan?: string | null
          session_token?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      lesson_plans: {
        Row: {
          class_subject_ids: string[]
          created_at: string
          days_selected: string[]
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id: string
          storage_file_path: string
          user_id: string
          week_id: string | null
          week_label: string
        }
        Insert: {
          class_subject_ids: string[]
          created_at?: string
          days_selected: string[]
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id?: string
          storage_file_path: string
          user_id: string
          week_id?: string | null
          week_label?: string
        }
        Update: {
          class_subject_ids?: string[]
          created_at?: string
          days_selected?: string[]
          file_mime_type?: string
          file_name?: string
          file_size_bytes?: number
          id?: string
          storage_file_path?: string
          user_id?: string
          week_id?: string | null
          week_label?: string
        }
        Relationships: [
          {
            foreignKeyName: "lesson_plans_week_id_fkey"
            columns: ["week_id"]
            isOneToOne: false
            referencedRelation: "rph_weeks"
            referencedColumns: ["id"]
          },
        ]
      }
      observation_schedules: {
        Row: {
          class_subject_id: string
          created_at: string
          id: string
          notes: string | null
          observation_date: string
          observer_name: string
          observer_position: string
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          class_subject_id: string
          created_at?: string
          id?: string
          notes?: string | null
          observation_date: string
          observer_name: string
          observer_position: string
          status?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          class_subject_id?: string
          created_at?: string
          id?: string
          notes?: string | null
          observation_date?: string
          observer_name?: string
          observer_position?: string
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "observation_schedules_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          class_subjects: Json | null
          created_at: string | null
          full_name: string | null
          gender: string | null
          id: string
          is_profile_complete: boolean | null
          role: string | null
          time_slots: Json | null
          updated_at: string | null
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          class_subjects?: Json | null
          created_at?: string | null
          full_name?: string | null
          gender?: string | null
          id: string
          is_profile_complete?: boolean | null
          role?: string | null
          time_slots?: Json | null
          updated_at?: string | null
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          class_subjects?: Json | null
          created_at?: string | null
          full_name?: string | null
          gender?: string | null
          id?: string
          is_profile_complete?: boolean | null
          role?: string | null
          time_slots?: Json | null
          updated_at?: string | null
          username?: string | null
        }
        Relationships: []
      }
      dskp_documents: {
        Row: {
          class_id: string
          class_name: string
          created_at: string
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id: string
          storage_file_path: string
          subject_id: string
          subject_name: string
          updated_at: string
          user_id: string
        }
        Insert: {
          class_id: string
          class_name: string
          created_at?: string
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id?: string
          storage_file_path: string
          subject_id: string
          subject_name: string
          updated_at?: string
          user_id: string
        }
        Update: {
          class_id?: string
          class_name?: string
          created_at?: string
          file_mime_type?: string
          file_name?: string
          file_size_bytes?: number
          id?: string
          storage_file_path?: string
          subject_id?: string
          subject_name?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "dskp_documents_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "dskp_documents_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }      
      rpt_documents: {
        Row: {
          class_id: string
          class_name: string
          created_at: string
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id: string
          storage_file_path: string
          subject_id: string
          subject_name: string
          updated_at: string
          user_id: string
        }
        Insert: {
          class_id: string
          class_name: string
          created_at?: string
          file_mime_type: string
          file_name: string
          file_size_bytes: number
          id?: string
          storage_file_path: string
          subject_id: string
          subject_name: string
          updated_at?: string
          user_id: string
        }
        Update: {
          class_id?: string
          class_name?: string
          created_at?: string
          file_mime_type?: string
          file_name?: string
          file_size_bytes?: number
          id?: string
          storage_file_path?: string
          subject_id?: string
          subject_name?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "rpt_documents_subject_id_fkey"
            columns: ["subject_id"]
            isOneToOne: false
            referencedRelation: "subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rpt_documents_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      rph_weeks: {
        Row: {
          created_at: string
          end_date: string | null
          id: string
          name: string
          start_date: string | null
          theme: string | null
          topic: string | null
          updated_at: string
          user_id: string
          week_number: number
        }
        Insert: {
          created_at?: string
          end_date?: string | null
          id?: string
          name: string
          start_date?: string | null
          theme?: string | null
          topic?: string | null
          updated_at?: string
          user_id: string
          week_number: number
        }
        Update: {
          created_at?: string
          end_date?: string | null
          id?: string
          name?: string
          start_date?: string | null
          theme?: string | null
          topic?: string | null
          updated_at?: string
          user_id?: string
          week_number?: number
        }
        Relationships: []
      }
      subjects: {
        Row: {
          code: string
          created_at: string
          id: string
          is_custom: boolean
          name: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          code: string
          created_at?: string
          id?: string
          is_custom?: boolean
          name: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          code?: string
          created_at?: string
          id?: string
          is_custom?: boolean
          name?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: []
      }
      teacher_schedules: {
        Row: {
          created_at: string
          id: string
          lesson_plan_id: string | null
          schedule_details: Json
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          lesson_plan_id?: string | null
          schedule_details?: Json
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          lesson_plan_id?: string | null
          schedule_details?: Json
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "teacher_schedules_lesson_plan_fkey"
            columns: ["lesson_plan_id"]
            isOneToOne: false
            referencedRelation: "lesson_plans"
            referencedColumns: ["id"]
          },
        ]
      }
      teacher_schedules_2025: {
        Row: {
          created_at: string
          id: string
          lesson_plan_id: string | null
          schedule_details: Json
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          lesson_plan_id?: string | null
          schedule_details?: Json
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          lesson_plan_id?: string | null
          schedule_details?: Json
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      teacher_schedules_2026: {
        Row: {
          created_at: string
          id: string
          lesson_plan_id: string | null
          schedule_details: Json
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          lesson_plan_id?: string | null
          schedule_details?: Json
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          lesson_plan_id?: string | null
          schedule_details?: Json
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      teacher_schedules_historical: {
        Row: {
          created_at: string
          id: string
          lesson_plan_id: string | null
          schedule_details: Json
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          lesson_plan_id?: string | null
          schedule_details?: Json
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          lesson_plan_id?: string | null
          schedule_details?: Json
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      timetable_entries: {
        Row: {
          activity_description: string | null
          activity_title: string | null
          activity_type: Database["public"]["Enums"]["activity_type_enum"]
          class_id: string | null
          class_name: string | null
          created_at: string
          day: string
          id: string
          notes: string | null
          room: string | null
          subject_id: string | null
          subject_name: string | null
          teacher_schedule_created_at: string | null
          teacher_schedule_id: string | null
          time_slot_end: string
          time_slot_start: string
          updated_at: string
          user_id: string
        }
        Insert: {
          activity_description?: string | null
          activity_title?: string | null
          activity_type?: Database["public"]["Enums"]["activity_type_enum"]
          class_id?: string | null
          class_name?: string | null
          created_at?: string
          day: string
          id?: string
          notes?: string | null
          room?: string | null
          subject_id?: string | null
          subject_name?: string | null
          teacher_schedule_created_at?: string | null
          teacher_schedule_id?: string | null
          time_slot_end: string
          time_slot_start: string
          updated_at?: string
          user_id: string
        }
        Update: {
          activity_description?: string | null
          activity_title?: string | null
          activity_type?: Database["public"]["Enums"]["activity_type_enum"]
          class_id?: string | null
          class_name?: string | null
          created_at?: string
          day?: string
          id?: string
          notes?: string | null
          room?: string | null
          subject_id?: string | null
          subject_name?: string | null
          teacher_schedule_created_at?: string | null
          teacher_schedule_id?: string | null
          time_slot_end?: string
          time_slot_start?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "timetable_entries_teacher_schedule_fkey"
            columns: ["teacher_schedule_id", "teacher_schedule_created_at"]
            isOneToOne: false
            referencedRelation: "teacher_schedules"
            referencedColumns: ["id", "created_at"]
          },
        ]
      }
      user_week_submissions: {
        Row: {
          created_at: string
          id: string
          reviewed_at: string | null
          reviewer_id: string | null
          status: Database["public"]["Enums"]["submission_status_enum"]
          submitted_at: string | null
          supervisor_comments: string | null
          updated_at: string
          user_id: string
          week_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          reviewed_at?: string | null
          reviewer_id?: string | null
          status?: Database["public"]["Enums"]["submission_status_enum"]
          submitted_at?: string | null
          supervisor_comments?: string | null
          updated_at?: string
          user_id: string
          week_id: string
        }
        Update: {
          created_at?: string
          id?: string
          reviewed_at?: string | null
          reviewer_id?: string | null
          status?: Database["public"]["Enums"]["submission_status_enum"]
          submitted_at?: string | null
          supervisor_comments?: string | null
          updated_at?: string
          user_id?: string
          week_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_week_submissions_week_id_fkey"
            columns: ["week_id"]
            isOneToOne: false
            referencedRelation: "rph_weeks"
            referencedColumns: ["id"]
          },
        ]
      }
      tindakan_susulan: {
        Row: {
          created_at: string
          id: string
          is_default: boolean
          option_text: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          is_default?: boolean
          option_text: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          is_default?: boolean
          option_text?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tindakan_susulan_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      tidak_terlaksana: {
        Row: {
          created_at: string
          id: string
          is_default: boolean
          option_text: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          is_default?: boolean
          option_text: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          is_default?: boolean
          option_text?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tidak_terlaksana_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      user_preferences: {
        Row: {
          created_at: string
          id: string
          notification_settings: Json
          onboarding_state: Json
          ui_preferences: Json
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          notification_settings?: Json
          onboarding_state?: Json
          ui_preferences?: Json
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          notification_settings?: Json
          onboarding_state?: Json
          ui_preferences?: Json
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_preferences_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      teacher_activities: {
        Row: {
          activity_description: string
          category: string
          created_at: string
          end_date: string | null
          id: string
          is_active: boolean
          location: string | null
          notes: string | null
          start_date: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          activity_description: string
          category: string
          created_at?: string
          end_date?: string | null
          id?: string
          is_active?: boolean
          location?: string | null
          notes?: string | null
          start_date?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          activity_description?: string
          category?: string
          created_at?: string
          end_date?: string | null
          id?: string
          is_active?: boolean
          location?: string | null
          notes?: string | null
          start_date?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "teacher_activities_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      teacher_tasks: {
        Row: {
          category: string
          created_at: string
          due_date: string | null
          id: string
          is_completed: boolean
          notes: string | null
          priority: string
          task_description: string
          updated_at: string
          user_id: string
        }
        Insert: {
          category: string
          created_at?: string
          due_date?: string | null
          id?: string
          is_completed?: boolean
          notes?: string | null
          priority?: string
          task_description: string
          updated_at?: string
          user_id: string
        }
        Update: {
          category?: string
          created_at?: string
          due_date?: string | null
          id?: string
          is_completed?: boolean
          notes?: string | null
          priority?: string
          task_description?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "teacher_tasks_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      delete_user_by_id: {
        Args: { user_id: number } | { user_id: string }
        Returns: undefined
      }
    }
    Enums: {
      activity_type_enum:
        | "CLASS"
        | "ASSEMBLY"
        | "COCURRICULAR"
        | "MEETING"
        | "BREAK"
        | "OTHER"
      submission_status_enum: "Draf" | "Dihantar" | "Disemak" | "Ditolak"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

// Simplified type exports for better compatibility
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type TablesInsert<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type TablesUpdate<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T]
