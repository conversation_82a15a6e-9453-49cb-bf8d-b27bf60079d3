import { defineNuxtConfig } from "nuxt/config";
import dotenv from 'dotenv'

// Load environment variables from .env.local
dotenv.config({ path: '.env.local' })

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: "2025-05-15",
  css: ["~/assets/css/tailwind.css"], // Removed effects.css as it's imported in tailwind.css

  // Multi-tenant configuration
  runtimeConfig: {
    // Private keys (server-side only)
    stripeSecretKey: process.env.STRIPE_SECRET_KEY,
    stripeWebhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
    supabaseServiceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,

    public: {
      baseDomain: process.env.NUXT_PUBLIC_BASE_DOMAIN || (
        process.env.NODE_ENV === 'production'
          ? 'rphmate.com'
          : 'localhost:3000'
      ),
      devMode: process.env.NUXT_PUBLIC_DEV_MODE || 'false',
      enableSubdomains: process.env.NUXT_PUBLIC_ENABLE_SUBDOMAINS || 'true',
      devSampleSchools: process.env.DEV_SAMPLE_SCHOOLS || 'xba1224,demo,test,school1,school2',
      supabaseUrl: process.env.SUPABASE_URL,
      supabaseAnonKey: process.env.SUPABASE_ANON_KEY,
      stripePublishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
    }
  },

  modules: [
    "@pinia/nuxt",
    "@nuxtjs/tailwindcss",
    "@nuxtjs/supabase",
    [
      "@nuxtjs/google-fonts",
      {
        families: {
          Poppins: [400, 500, 600, 700],
        },
        preload: true,
        display: "swap",
      },
    ],
    "@nuxtjs/color-mode",
    "@nuxt/icon", // Updated from nuxt-icon to @nuxt/icon
  ],
  supabase: {
    url: "https://nhgyywlfopodxomxbegx.supabase.co",
    key: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5oZ3l5d2xmb3BvZHhvbXhiZWd4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcyMTU1OTgsImV4cCI6MjA2Mjc5MTU5OH0.OaAqdBGba5YxutbQ8oIE_KULFGXdkBcfFzmdYUav4Sc",
    redirectOptions: {
      login: '/disabled-login',
      callback: '/disabled-callback',
      exclude: ['/*'] // Exclude all routes from automatic redirects
    },
    cookieOptions: {
      maxAge: 60 * 60 * 8, // 8 hours
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    },
  },
  typescript: {
    strict: true,
    typeCheck: true, // Enable type checking during development and build
  },

  devtools: { enabled: true },

  colorMode: {
    preference: "system", // default value of $colorMode.preference
    fallback: "light", // fallback value if not system preference found
    classSuffix: "",
  },

  // Multi-tenant routing rules
  routeRules: {
    // Landing page routes (main domain)
    "/": { prerender: true },
    "/pricing": { prerender: true },
    "/features": { prerender: true },
    "/contact": { prerender: true },

    // Admin routes (main domain)
    "/admin/**": { ssr: true },

    // School routes (subdomains) - dynamic routes handled by middleware
    // These will be handled by the subdomain middleware
  },

  // Nitro configuration for subdomain support
  nitro: {
    experimental: {
      wasm: true
    },
    // Subdomain routing will be handled by middleware and plugins
    // Development subdomain support is handled by the subdomain detection plugins
  },

  // Development server configuration
  devServer: {
    host: '0.0.0.0', // Allow external connections for subdomain testing
    port: 3000
  },

  // Server-side rendering configuration
  ssr: true,

  // App configuration
  app: {
    head: {
      title: 'RPHMate - Streamline Your Teaching Journey',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'description', content: 'Comprehensive educational platform for lesson planning, progress tracking, and enhanced teaching experiences.' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
      ]
    }
  },

  // Build configuration
  build: {
    transpile: ['@nuxtjs/supabase']
  }
});
