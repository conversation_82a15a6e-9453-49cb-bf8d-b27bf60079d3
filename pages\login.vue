<template>
  <section class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Header -->
      <div class="text-center">
        <NuxtLink to="/" class="flex items-center justify-center space-x-2 mb-6">
          <UiBaseIcon name="heroicons:academic-cap" class="h-12 w-12 text-blue-600" />
          <span class="text-2xl font-bold text-gray-900 dark:text-white">RPHMate</span>
        </NuxtLink>
        <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white">
          School Admin Sign In
        </h2>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
          Access your school management dashboard
        </p>
      </div>

      <!-- Login Form -->
      <form @submit.prevent="handleSubmit" class="mt-8 space-y-6">
        <div class="space-y-4">
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Email Address
            </label>
            <input id="email" v-model="form.email" type="email" required placeholder="Enter your email"
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" />
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Password
            </label>
            <div class="mt-1 relative">
              <input id="password" v-model="form.password" :type="showPassword ? 'text' : 'password'" required
                placeholder="Enter your password"
                class="appearance-none relative block w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" />
              <button type="button" @click="showPassword = !showPassword"
                class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <UiBaseIcon :name="showPassword ? 'heroicons:eye-slash' : 'heroicons:eye'"
                  class="h-5 w-5 text-gray-400 hover:text-gray-600" />
              </button>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input id="remember-me" v-model="form.rememberMe" type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
              <label for="remember-me" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Remember me
              </label>
            </div>

            <div class="text-sm">
              <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
                Forgot your password?
              </a>
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div v-if="errorMessage"
          class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div class="flex">
            <UiBaseIcon name="heroicons:exclamation-triangle" class="h-5 w-5 text-red-400" />
            <div class="ml-3">
              <p class="text-sm text-red-800 dark:text-red-200">{{ errorMessage }}</p>
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <div>
          <button type="submit" :disabled="isLoading"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-400 disabled:cursor-not-allowed">
            <span v-if="isLoading" class="absolute left-0 inset-y-0 flex items-center pl-3">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            </span>
            {{ isLoading ? 'Signing in...' : 'Sign in' }}
          </button>
        </div>

        <!-- Sign Up Link -->
        <div class="text-center">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            Don't have a school account?
            <NuxtLink to="/pricing" class="font-medium text-blue-600 hover:text-blue-500">
              Start your free trial
            </NuxtLink>
          </p>
        </div>

        <!-- Divider -->
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400">
              Or continue with
            </span>
          </div>
        </div>

        <!-- Google Sign In -->
        <div>
          <button type="button" @click="signInWithGoogle" :disabled="isGoogleLoading"
            class="group relative w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
            <span v-if="isGoogleLoading" class="absolute left-0 inset-y-0 flex items-center pl-3">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
            </span>
            <UiBaseIcon v-if="!isGoogleLoading" name="logos:google-icon" class="w-5 h-5 mr-2" />
            {{ isGoogleLoading ? 'Connecting...' : 'Sign in with Google' }}
          </button>
        </div>

        <!-- Divider -->
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400">
              Or access your school directly
            </span>
          </div>
        </div>

        <!-- School Access -->
        <div class="space-y-3">
          <div>
            <label for="schoolCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              School Code
            </label>
            <div class="mt-1 flex rounded-md shadow-sm">
              <input id="schoolCode" v-model="schoolCode" type="text" placeholder="e.g., xba1224"
                class="flex-1 min-w-0 block w-full px-3 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500 sm:text-sm" />
              <button type="button" @click="goToSchool" :disabled="!schoolCode.trim()"
                class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 dark:border-gray-600 rounded-r-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-300 text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-100 dark:hover:bg-gray-600">
                Go
              </button>
            </div>
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
              Enter your school code to access your school portal
            </p>
          </div>
        </div>
      </form>
    </div>

    <!-- Error Modal -->
    <div v-if="showErrorModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeErrorModal"></div>

        <!-- Modal panel -->
        <div
          class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div
                class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 sm:mx-0 sm:h-10 sm:w-10">
                <UiBaseIcon name="heroicons:information-circle" class="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                  {{ errorModalTitle }}
                </h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    {{ errorModalMessage }}
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="button" @click="goToRegistration"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
              Daftar Sekolah
            </button>
            <button type="button" @click="closeErrorModal"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
              Cuba Lagi
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  layout: 'landing'
})

// Form state
const form = ref({
  email: '',
  password: '',
  rememberMe: false
})

const showPassword = ref(false)
const isLoading = ref(false)
const isGoogleLoading = ref(false)
const errorMessage = ref('')
const schoolCode = ref('')

// Modal state for access denied
const showErrorModal = ref(false)
const errorModalTitle = ref('')
const errorModalMessage = ref('')

// Handle form submission
const handleSubmit = async () => {
  isLoading.value = true
  errorMessage.value = ''

  try {
    const supabase = useSupabaseClient()

    // Authenticate with Supabase
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: form.value.email,
      password: form.value.password
    })

    if (authError) {
      throw new Error(authError.message)
    }

    if (!authData.user) {
      throw new Error('Authentication failed')
    }

    // Get user's schools to determine where to redirect
    const schoolsResponse = await $fetch('/api/schools/user-schools', {
      headers: {
        'Authorization': `Bearer ${authData.session.access_token}`
      }
    }).catch(err => ({ error: err, success: false, schools: [] })) as any

    if (schoolsResponse.error || !schoolsResponse?.success) {
      throw new Error('Unable to access school information. Please ensure you have a registered school.')
    }

    const userSchools = schoolsResponse.schools

    if (!userSchools || userSchools.length === 0) {
      throw new Error('No schools found for your account. Please register your school first.')
    }

    // If user has multiple schools, redirect to the first one
    // In the future, you might want to show a school selection page
    const primarySchool = userSchools[0]

    // Redirect to school admin dashboard
    await navigateTo(`/${primarySchool.code}`)

  } catch (error: any) {
    console.error('Login error:', error)
    errorMessage.value = error.message || 'Invalid email or password. Please try again.'
  } finally {
    isLoading.value = false
  }
}

// Go to school portal
const goToSchool = () => {
  if (schoolCode.value.trim()) {
    // In development, redirect to subdomain simulation
    if (process.env.NODE_ENV === 'development') {
      window.location.href = `http://${schoolCode.value.trim()}.localhost:3000`
    } else {
      window.location.href = `https://${schoolCode.value.trim()}.rphmate.com`
    }
  }
}

// Google Sign-in with validation
const signInWithGoogle = async () => {
  if (isGoogleLoading.value) return

  isGoogleLoading.value = true
  errorMessage.value = ''

  try {
    const supabase = useSupabaseClient()

    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/login?validate=true`
      }
    })

    if (error) throw error

  } catch (error: any) {
    errorMessage.value = error.message || 'Failed to sign in with Google'
    isGoogleLoading.value = false
  }
}

// Validate school admin access after Google OAuth
const validateSchoolAdmin = async () => {
  try {
    const supabase = useSupabaseClient()
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      showErrorModal.value = true
      errorModalTitle.value = 'Pengesahan Gagal'
      errorModalMessage.value = 'Sila cuba log masuk semula.'
      return
    }

    // Check if user has schools (is admin)
    const response = await $fetch('/api/schools/user-schools', {
      headers: {
        'Authorization': `Bearer ${session.access_token}`
      }
    }).catch(() => ({ success: false, schools: [] }))

    if (!response.success || response.schools.length === 0) {
      // NOT a school admin - show error modal and sign out
      await supabase.auth.signOut()

      showErrorModal.value = true
      errorModalTitle.value = 'Akses Dinafikan'
      errorModalMessage.value = 'Anda perlu daftar langganan sebelum boleh masuk mendaftar sebagai pentadbir RPHMate sekolah.'
      return
    }

    // IS a school admin - redirect to dashboard
    const primarySchool = response.schools[0]
    await navigateTo(`/${primarySchool.code}`)

  } catch (error) {
    console.error('Validation error:', error)
    const supabase = useSupabaseClient()
    await supabase.auth.signOut()

    showErrorModal.value = true
    errorModalTitle.value = 'Ralat Pengesahan'
    errorModalMessage.value = 'Ralat berlaku semasa mengesahkan akses anda. Sila cuba lagi.'
  }
}

// Modal functions
const closeErrorModal = () => {
  showErrorModal.value = false
  // Clear the validate query parameter
  const router = useRouter()
  router.replace('/login')
}

const goToRegistration = () => {
  showErrorModal.value = false
  navigateTo('/pricing')
}

// Check for validation on page load
onMounted(async () => {
  const route = useRoute()
  if (route.query.validate === 'true') {
    await validateSchoolAdmin()
  }
})

// Set page head
useHead({
  title: 'School Admin Sign In - RPHMate',
  meta: [
    {
      name: 'description',
      content: 'Sign in to your RPHMate school admin dashboard to manage your school, teachers, and billing.'
    }
  ]
})
</script>

<style scoped>
/* Custom styles for login page */
</style>
