<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Progress Bar -->
    <RegistrationProgress :current-step="3" />

    <!-- Main Content -->
    <section class="py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
          <h2 class="text-3xl font-extrabold text-gray-900 dark:text-white">
            Pilih Pelan & Pembayaran
          </h2>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">
            Langkah 3: <PERSON>lih pelan langganan dan selesaikan pembayaran
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Left Column: School Summary -->
          <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 sticky top-8">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                <PERSON><PERSON><PERSON>
              </h3>
              <div v-if="schoolData" class="space-y-3 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600 dark:text-gray-300">Nama Sekolah:</span>
                  <span class="font-medium text-gray-900 dark:text-white">{{ schoolData.school_name }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600 dark:text-gray-300">Kod Sekolah:</span>
                  <span class="font-medium text-gray-900 dark:text-white">{{ schoolData.school_code }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600 dark:text-gray-300">Pentadbir:</span>
                  <span class="font-medium text-gray-900 dark:text-white">{{ schoolData.admin_full_name }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600 dark:text-gray-300">Emel:</span>
                  <span class="font-medium text-gray-900 dark:text-white">{{ schoolData.admin_email }}</span>
                </div>
              </div>
              <div v-else class="text-center text-gray-500 dark:text-gray-400">
                Memuatkan maklumat sekolah...
              </div>

              <!-- School URL Preview -->
              <div v-if="schoolData" class="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <p class="text-xs text-blue-700 dark:text-blue-300 mb-1">URL Portal Sekolah:</p>
                <p class="text-sm font-medium text-blue-900 dark:text-blue-200">
                  {{ schoolUrl }}
                </p>
              </div>
            </div>
          </div>

          <!-- Right Column: Plan Selection & Payment -->
          <div class="lg:col-span-2 space-y-8">

            <!-- Plan Selection -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-6">
                Pilih Pelan Langganan
              </h3>

              <div class="space-y-4">
                <!-- Basic Plan -->
                <div class="relative border rounded-lg p-4 cursor-pointer transition-all duration-200"
                  :class="selectedPlan === 'basic' ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'"
                  @click="selectedPlan = 'basic'">
                  <div class="flex items-center">
                    <input type="radio" name="plan" value="basic" v-model="selectedPlan"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" />
                    <div class="ml-3 flex-1">
                      <div class="flex justify-between items-center">
                        <div>
                          <p class="text-sm font-medium text-gray-900 dark:text-white">
                            Pelan Asas - RM99/bulan
                          </p>
                          <p class="text-sm text-gray-500 dark:text-gray-400">
                            Sehingga 10 guru
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Professional Plan -->
                <div class="relative border rounded-lg p-4 cursor-pointer transition-all duration-200"
                  :class="selectedPlan === 'professional' ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'"
                  @click="selectedPlan = 'professional'">
                  <div class="flex items-center">
                    <input type="radio" name="plan" value="professional" v-model="selectedPlan"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" />
                    <div class="ml-3 flex-1">
                      <div class="flex justify-between items-center">
                        <div>
                          <p class="text-sm font-medium text-gray-900 dark:text-white">
                            Pelan Profesional - RM199/bulan
                          </p>
                          <p class="text-sm text-gray-500 dark:text-gray-400">
                            Sehingga 50 guru (Paling Popular)
                          </p>
                        </div>
                        <span
                          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                          Popular
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Enterprise Plan -->
                <div class="relative border rounded-lg p-4 cursor-pointer transition-all duration-200"
                  :class="selectedPlan === 'enterprise' ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'"
                  @click="selectedPlan = 'enterprise'">
                  <div class="flex items-center">
                    <input type="radio" name="plan" value="enterprise" v-model="selectedPlan"
                      class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300" />
                    <div class="ml-3 flex-1">
                      <div class="flex justify-between items-center">
                        <div>
                          <p class="text-sm font-medium text-gray-900 dark:text-white">
                            Pelan Perusahaan - RM399/bulan
                          </p>
                          <p class="text-sm text-gray-500 dark:text-gray-400">
                            Guru tanpa had
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Coupon Code -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Kod Kupon (Pilihan)
              </h3>
              <input v-model="couponCode" type="text" placeholder="Masukkan kod kupon"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                :disabled="loading" />
            </div>

            <!-- Error Message -->
            <div v-if="errorMessage"
              class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
              <p class="text-sm text-red-600 dark:text-red-400">{{ errorMessage }}</p>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-between pt-6">
              <button type="button" @click="goBack"
                class="flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <Icon name="heroicons:arrow-left-20-solid" class="w-4 h-4 mr-2" />
                Kembali
              </button>

              <button type="button" @click="handleSubmit" :disabled="loading || !schoolData"
                class="flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed">
                <Icon v-if="loading" name="heroicons:arrow-path-20-solid" class="animate-spin -ml-1 mr-2 h-4 w-4" />
                {{ loading ? 'Memproses...' : 'Mula Percubaan Percuma' }}
                <Icon v-if="!loading" name="heroicons:arrow-right-20-solid" class="w-4 h-4 ml-2" />
              </button>
            </div>
          </div>
        </div>
      </div>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  layout: 'landing'
})

// Reactive data
const schoolData = ref<any>(null)
const selectedPlan = ref('professional')
const couponCode = ref('')
const loading = ref(false)
const errorMessage = ref('')
const sessionToken = ref('')

// Supabase client
const supabase = useSupabaseClient()

// Computed properties
const schoolUrl = computed(() => {
  if (!schoolData.value?.school_code) return ''

  if (process.env.NODE_ENV === 'development') {
    return `http://${schoolData.value.school_code}.localhost:3000`
  }
  return `https://${schoolData.value.school_code}.rphmate.com`
})

// Load school data on mount
onMounted(async () => {
  sessionToken.value = sessionStorage.getItem('registration_session_token') || ''

  if (!sessionToken.value) {
    await navigateTo('/daftar')
    return
  }

  // Load pre-billing data
  const { data, error } = await supabase
    .from('pre_billing')
    .select('*')
    .eq('session_token', sessionToken.value)
    .single()

  if (error || !data) {
    await navigateTo('/daftar')
    return
  }

  // Check if previous phases are completed
  if (data.phase_completed < 2) {
    if (data.phase_completed < 1) {
      await navigateTo('/daftar')
    } else {
      await navigateTo('/daftar/maklumat')
    }
    return
  }

  // Populate data
  schoolData.value = data
  selectedPlan.value = data.selected_plan || 'professional'
})

// Handle form submission (create Stripe checkout)
const handleSubmit = async () => {
  if (!schoolData.value) return

  loading.value = true
  errorMessage.value = ''

  try {
    // Update selected plan in pre-billing
    await supabase
      .from('pre_billing')
      .update({
        selected_plan: selectedPlan.value,
        updated_at: new Date().toISOString()
      })
      .eq('session_token', sessionToken.value)

    // Create Stripe checkout session
    const response = await $fetch('/api/stripe/create-checkout', {
      method: 'POST',
      body: {
        schoolCode: schoolData.value.school_code,
        schoolName: schoolData.value.school_name,
        schoolAddress: schoolData.value.school_address || '',
        adminEmail: schoolData.value.admin_email,
        adminFirstName: schoolData.value.admin_full_name.split(' ')[0] || '',
        adminLastName: schoolData.value.admin_full_name.split(' ').slice(1).join(' ') || '',
        selectedPlan: selectedPlan.value,
        couponCode: couponCode.value.trim() || undefined
      }
    }) as any

    if (response.success && response.checkoutUrl) {
      // Redirect to Stripe checkout
      window.location.href = response.checkoutUrl
    } else {
      throw new Error('Failed to create checkout session')
    }

  } catch (error: any) {
    console.error('Checkout creation error:', error)
    errorMessage.value = error.message || 'Ralat mencipta sesi pembayaran. Sila cuba lagi.'
  } finally {
    loading.value = false
  }
}

// Go back to previous phase
const goBack = async () => {
  await navigateTo('/daftar/maklumat')
}

// Set page head
useHead({
  title: 'Pembayaran - RPHMate',
  meta: [
    {
      name: 'description',
      content: 'Pilih pelan langganan dan selesaikan pembayaran untuk akaun sekolah anda.'
    }
  ]
})
</script>
